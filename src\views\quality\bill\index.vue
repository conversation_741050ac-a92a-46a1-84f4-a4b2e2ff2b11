<template>
  <div class="app-container">
    <div class="app-container-div">
      <el-form
        :model="queryParams"
        ref="queryForm"
        size="small"
        :inline="true"
        v-show="showSearch"
        label-width="68px"
      >
        <el-form-item label="质检单号" prop="iqcNo">
          <el-input
            v-model="queryParams.iqcNo"
            placeholder="请输入质检单号"
            clearable
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="入库单号" prop="stockInNo">
          <el-input
            v-model="queryParams.stockInNo"
            placeholder="请输入入库单号"
            clearable
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="收货单号" prop="receiveNo">
          <el-input
            v-model="queryParams.receiveNo"
            placeholder="请输入收货单号"
            clearable
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="供应商编码" prop="supplierCode">
          <el-input
            v-model="queryParams.supplierCode"
            placeholder="请输入供应商编码"
            clearable
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="供应商名称" prop="supplierName">
          <el-input
            v-model="queryParams.supplierName"
            placeholder="请输入供应商名称"
            clearable
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="物料编码" prop="materialCode">
          <el-input
            v-model="queryParams.materialCode"
            placeholder="请输入物料编码"
            clearable
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="物料名称" prop="materialName">
          <el-input
            v-model="queryParams.materialName"
            placeholder="请输入物料名称"
            clearable
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="批次" prop="batchNo">
          <el-input
            v-model="queryParams.batchNo"
            placeholder="请输入批次"
            clearable
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item
          label="	
质检结果"
          prop="inspResult"
        >
          <el-select
            v-model="queryParams.inspResult"
            placeholder="请选择质检状态"
            clearable
          >
            <el-option
              v-for="dict in dict.type.insp_result"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="质检状态" prop="billStatus">
          <el-select
            v-model="queryParams.billStatus"
            placeholder="请选择质检状态"
            clearable
          >
            <el-option
              v-for="dict in dict.type.bill_status"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="采购单号" prop="purchaseNo">
          <el-input
            v-model="queryParams.purchaseNo"
            placeholder="请输入采购单号"
            clearable
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item>
          <el-button
            type="primary"
            icon="el-icon-search"
            size="mini"
            @click="handleQuery"
            >搜索</el-button
          >
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
            >重置</el-button
          >
        </el-form-item>
      </el-form>

      <el-row :gutter="10" class="mb8">
        <!-- <el-col :span="1.5">
          <el-button
            type="primary"
            plain
            icon="el-icon-plus"
            size="mini"
            @click="handleAdd"
            v-hasPermi="['quality:bill:add']"
            >新增</el-button
          >
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="success"
            plain
            icon="el-icon-edit"
            size="mini"
            :disabled="single"
            @click="handleUpdate"
            v-hasPermi="['quality:bill:edit']"
            >修改</el-button
          >
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="danger"
            plain
            icon="el-icon-delete"
            size="mini"
            :disabled="multiple"
            @click="handleDelete"
            v-hasPermi="['quality:bill:remove']"
            >删除</el-button
          >
        </el-col> -->
        <el-col :span="1.5">
          <el-button
            type="warning"
            plain
            icon="el-icon-download"
            size="mini"
            @click="handleExport"
            v-hasPermi="['quality:bill:export']"
            >导出</el-button
          >
        </el-col>
        <right-toolbar
          :showSearch.sync="showSearch"
          @queryTable="getList"
        ></right-toolbar>
      </el-row>

      <el-table
        height="62vh"
        v-loading="loading"
        :data="billList"
        @selection-change="handleSelectionChange"
      >
        <el-table-column align="center" type="index" width="50" />
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column
          label="质检单号"
          align="center"
          prop="iqcNo"
          :width="tableWidth(billList.map((x) => x.iqcNo))"
        >
          <template slot-scope="scope">
            <div class="inputInfo copy_icon">
              <el-tooltip
                placement="top"
                effect="dark"
                :content="scope.row.iqcNo"
              >
                <span class="ellipsis" style="display: inline-block">{{
                  scope.row.iqcNo
                }}</span>
              </el-tooltip>
              <i
                style="margin-left: 10px; cursor: pointer"
                class="el-icon-document-copy"
                v-clipboard:copy="scope.row.iqcNo"
                v-clipboard:success="onCopy"
              ></i>
            </div>
          </template>
        </el-table-column>
        <el-table-column
          label="入库单号"
          align="center"
          prop="stockInNo"
          width="155"
        />
        <el-table-column
          label="收货单号"
          align="center"
          prop="receiveNo"
          width="150"
        />
        <el-table-column
          label="采购单号"
          align="center"
          prop="purchaseNo"
          width="150"
        />
        <el-table-column
          label="供应商编码"
          align="center"
          prop="supplierCode"
          width="120"
        />
        <el-table-column
          label="供应商名称"
          align="center"
          prop="supplierName"
          width="120"
        />
        <el-table-column
          label="物料编码"
          align="center"
          prop="materialCode"
          width="120"
        />
        <el-table-column
          label="物料名称"
          align="center"
          prop="materialName"
          width="120"
        />
        <el-table-column label="数量" align="center" prop="qty" />
        <el-table-column label="批次" align="center" prop="batchNo" />
        <el-table-column label="严格度" align="center" prop="adjustedSeverity">
          <template slot-scope="scope">
            <dict-tag
              :options="dict.type.iqc_insp_config_strictness"
              :value="scope.row.adjustedSeverity"
            />
          </template>
        </el-table-column>
        <el-table-column label="质检结果" align="center" prop="inspResult">
          <template slot-scope="scope">
            <dict-tag
              :options="dict.type.insp_result"
              :value="scope.row.inspResult"
            />
          </template>
        </el-table-column>
        <el-table-column label="质检状态" align="center" prop="billStatus">
          <template slot-scope="scope">
            <dict-tag
              :options="dict.type.bill_status"
              :value="scope.row.billStatus"
            />
          </template>
        </el-table-column>
        <!-- <el-table-column
          label="是否已免检；"
          align="center"
          prop="isExemption"
        /> -->
        <el-table-column label="检验维度" align="center" prop="inspectionFrom">
          <template slot-scope="scope">
            <dict-tag
              :options="dict.type.inspection_from"
              :value="scope.row.inspectionFrom"
            />
          </template>
        </el-table-column>
        <el-table-column label="报检类型" align="center" prop="inspType">
          <template slot-scope="scope">
            <dict-tag
              :options="dict.type.insp_type"
              :value="scope.row.inspType"
            />
          </template>
        </el-table-column>
        <el-table-column
          label="生产日期"
          align="center"
          prop="dateCode"
          width="180"
        >
          <template slot-scope="scope">
            <span>{{ parseTime(scope.row.dateCode, "{y}-{m}-{d}") }}</span>
          </template>
        </el-table-column>
        <el-table-column
          label="保质期"
          align="center"
          prop="expirationDate"
          width="180"
        >
          <template slot-scope="scope">
            <span>{{
              parseTime(scope.row.expirationDate, "{y}-{m}-{d}")
            }}</span>
          </template>
        </el-table-column>
        <el-table-column
          label="检验日期"
          align="center"
          prop="inspectionDate"
          width="180"
        >
          <template slot-scope="scope">
            <span>{{
              parseTime(scope.row.inspectionDate, "{y}-{m}-{d}")
            }}</span>
          </template>
        </el-table-column>

        <el-table-column
          label="操作"
          align="center"
          class-name="small-padding fixed-width"
          fixed="right"
          width="120"
        >
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="text"
              icon="el-icon-s-custom"
              @click="handleQuality(scope.row)"
              v-hasPermi="['quality:bill:edit']"
              v-if="
                scope.row.billStatus == 'INSPECTED' ||
                scope.row.billStatus == 'REJECT' ||
                scope.row.billStatus == 'INSPECTING'
              "
              >质检</el-button
            >
            <el-button
              size="mini"
              type="text"
              icon="el-icon-view"
              @click="handleSelect(scope.row)"
              >查看</el-button
            >
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />

      <!-- 添加或修改iqc质检单据对话框 -->
      <el-drawer
        :title="title"
        :visible.sync="open"
        :size="'50%'"
        append-to-body
      >
        <el-form ref="form" :model="form" :rules="rules">
          <el-form-item label="质检单号" prop="iqcNo" style="width: 240px">
            <el-input v-model="form.iqcNo" placeholder="请输入质检单号" />
          </el-form-item>
          <el-form-item label="入库单id" prop="stockInId" style="width: 240px">
            <el-input v-model="form.stockInId" placeholder="请输入入库单id" />
          </el-form-item>
          <el-form-item label="入库单号" prop="stockInNo" style="width: 240px">
            <el-input v-model="form.stockInNo" placeholder="请输入入库单号" />
          </el-form-item>
          <el-form-item
            label="入库单明细id"
            prop="stockInDetailId"
            style="width: 240px"
          >
            <el-input
              v-model="form.stockInDetailId"
              placeholder="请输入入库单明细id"
            />
          </el-form-item>
          <el-form-item label="收货单号" prop="receiveNo" style="width: 240px">
            <el-input v-model="form.receiveNo" placeholder="请输入收货单号" />
          </el-form-item>
          <el-form-item label="供应商id" prop="supplierId" style="width: 240px">
            <el-input v-model="form.supplierId" placeholder="请输入供应商id" />
          </el-form-item>
          <el-form-item
            label="供应商编码"
            prop="supplierCode"
            style="width: 240px"
          >
            <el-input
              v-model="form.supplierCode"
              placeholder="请输入供应商编码"
            />
          </el-form-item>
          <el-form-item
            label="供应商名称"
            prop="supplierName"
            style="width: 240px"
          >
            <el-input
              v-model="form.supplierName"
              placeholder="请输入供应商名称"
            />
          </el-form-item>
          <el-form-item label="物料id" prop="materialId" style="width: 240px">
            <el-input v-model="form.materialId" placeholder="请输入物料id" />
          </el-form-item>
          <el-form-item
            label="物料编码"
            prop="materialCode"
            style="width: 240px"
          >
            <el-input
              v-model="form.materialCode"
              placeholder="请输入物料编码"
            />
          </el-form-item>
          <el-form-item
            label="物料名称"
            prop="materialName"
            style="width: 240px"
          >
            <el-input
              v-model="form.materialName"
              placeholder="请输入物料名称"
            />
          </el-form-item>
          <el-form-item label="数量" prop="qty" style="width: 240px">
            <el-input v-model="form.qty" placeholder="请输入数量" />
          </el-form-item>
          <el-form-item label="批次" prop="batchNo" style="width: 240px">
            <el-input v-model="form.batchNo" placeholder="请输入批次" />
          </el-form-item>
          <el-form-item
            label="严格度；字典iqc_insp_config_strictness"
            prop="adjustedSeverity"
            style="width: 240px"
          >
            <el-input
              v-model="form.adjustedSeverity"
              placeholder="请输入严格度；字典iqc_insp_config_strictness"
            />
          </el-form-item>
          <el-form-item
            label="	
质检结果；字典insp_result"
            prop="inspResult"
            style="width: 240px"
          >
            <el-input
              v-model="form.inspResult"
              placeholder="请输入	
质检结果；字典insp_result"
            />
          </el-form-item>
          <el-form-item
            label="质检状态；字典bill_status"
            prop="billStatus"
            style="width: 240px"
          >
            <el-select
              v-model="form.billStatus"
              placeholder="请选择质检状态；字典bill_status"
              style="width: 240px"
            >
              <el-option
                v-for="dict in dict.type.bill_status"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item
            label="是否已免检；"
            prop="isExemption"
            style="width: 240px"
          >
            <el-input
              v-model="form.isExemption"
              placeholder="请输入是否已免检；"
            />
          </el-form-item>
          <el-form-item
            label="检验维度；字典inspection_from"
            prop="inspectionFrom"
            style="width: 240px"
          >
            <el-input
              v-model="form.inspectionFrom"
              placeholder="请输入检验维度；字典inspection_from"
            />
          </el-form-item>
          <el-form-item label="生产日期" prop="dateCode" style="width: 240px">
            <el-date-picker
              clearable
              v-model="form.dateCode"
              type="date"
              value-format="yyyy-MM-dd"
              placeholder="请选择生产日期"
            >
            </el-date-picker>
          </el-form-item>
          <el-form-item
            label="保质期"
            prop="expirationDate"
            style="width: 240px"
          >
            <el-date-picker
              clearable
              v-model="form.expirationDate"
              type="date"
              value-format="yyyy-MM-dd"
              placeholder="请选择保质期"
            >
            </el-date-picker>
          </el-form-item>
          <el-form-item
            label="检验日期"
            prop="inspectionDate"
            style="width: 240px"
          >
            <el-date-picker
              clearable
              v-model="form.inspectionDate"
              type="date"
              value-format="yyyy-MM-dd"
              placeholder="请选择检验日期"
            >
            </el-date-picker>
          </el-form-item>
          <el-form-item label="采购单号" prop="purchaseNo" style="width: 240px">
            <el-input v-model="form.purchaseNo" placeholder="请输入采购单号" />
          </el-form-item>
          <el-form-item label="备注" prop="remark" style="width: 700px">
            <el-input
              v-model="form.remark"
              type="textarea"
              placeholder="请输入内容"
            />
          </el-form-item>
          <el-form-item label="组织" prop="comId" style="width: 240px">
            <el-input v-model="form.comId" placeholder="请输入组织" />
          </el-form-item>
          <el-form-item label="删除标志" prop="delFlag" style="width: 240px">
            <el-input v-model="form.delFlag" placeholder="请输入删除标志" />
          </el-form-item>
        </el-form>
        <div class="demo-drawer__footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </el-drawer>
    </div>
  </div>
</template>

<script>
import {
  listBill,
  getBill,
  delBill,
  addBill,
  updateBill,
} from "@/api/quality/bill";

export default {
  name: "Bill",
  dicts: [
    "bill_status",
    "iqc_insp_config_strictness",
    "insp_result",
    "inspection_from",
    "inspection_from",
    "insp_type",
  ],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // iqc质检单据表格数据
      billList: [],
      // 质检单号
      iqcNos: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        iqcNo: null,
        stockInId: null,
        stockInNo: null,
        stockInDetailId: null,
        receiveNo: null,
        supplierId: null,
        supplierCode: null,
        supplierName: null,
        materialId: null,
        materialCode: null,
        materialName: null,
        qty: null,
        batchNo: null,
        adjustedSeverity: null,
        inspResult: null,
        billStatus: null,
        isExemption: null,
        inspectionFrom: null,
        inspType: null,
        dateCode: null,
        expirationDate: null,
        inspectionDate: null,
        purchaseNo: null,
        comId: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        materialId: [
          { required: true, message: "物料id不能为空", trigger: "blur" },
        ],
        materialCode: [
          { required: true, message: "物料编码不能为空", trigger: "blur" },
        ],
        materialName: [
          { required: true, message: "物料名称不能为空", trigger: "blur" },
        ],
        qty: [{ required: true, message: "数量不能为空", trigger: "blur" }],
        delFlag: [
          { required: true, message: "删除标志不能为空", trigger: "blur" },
        ],
      },
    };
  },
  created() {
    this.getList();

    // 监听来自质检页面的刷新事件
    if (this.$eventBus) {
      this.$eventBus.$on('refreshBillList', this.handleRefreshFromQuality);
    }
  },

  mounted() {
    // 检查路由参数，如果有刷新标识则刷新数据
    if (this.$route.query.refresh) {
      console.log('检测到刷新标识，重新加载数据...');
      this.getList();
    }
  },

  beforeDestroy() {
    // 移除事件监听
    if (this.$eventBus) {
      this.$eventBus.$off('refreshBillList', this.handleRefreshFromQuality);
    }
  },

  watch: {
    // 监听路由变化
    '$route'(to, from) {
      if (to.query.refresh && to.path === '/iqc/bill') {
        console.log('路由变化检测到刷新标识，重新加载数据...');
        this.getList();
      }
    }
  },
  methods: {
    /** 处理来自质检页面的刷新事件 */
    handleRefreshFromQuality() {
      console.log('收到来自质检页面的刷新事件，重新加载数据...');
      this.getList();
    },

    /** 查询iqc质检单据列表 */
    getList() {
      this.loading = true;
      listBill(this.queryParams).then((response) => {
        this.billList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    //查询
    handleSelect(row) {
      row.title = "质检单查看";
      row.status = "select";
      console.log(row);
      this.$router.push({ path: "/quality/index", query: row });
    },
    handleQuality(row) {
      //免检
      if (row.isExemption == "YES") {
        this.$confirm("此物料是否进行免检?", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        })
          .then(() => {
            //待质检
            row.billStatus = "REVIEW";
            //合格
            row.inspResult = "qualified";
            updateBill(row).then((response) => {
              this.$modal.msgSuccess("免检成功");
            });
          })
          .catch(() => {
            // this.$message({
            //   type: "info",
            //   message: "已取消删除",
            // });
          });
      } else {
        row.title = "质检单编辑";
        row.status = "update";
        //跳转质检页面
        this.$router.push({ path: "/quality/index", query: row });
      }
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        iqcNo: null,
        stockInId: null,
        stockInNo: null,
        stockInDetailId: null,
        receiveNo: null,
        supplierId: null,
        supplierCode: null,
        supplierName: null,
        materialId: null,
        materialCode: null,
        materialName: null,
        qty: null,
        batchNo: null,
        adjustedSeverity: null,
        inspResult: null,
        billStatus: null,
        isExemption: null,
        inspectionFrom: null,
        inspType: null,
        dateCode: null,
        expirationDate: null,
        inspectionDate: null,
        purchaseNo: null,
        remark: null,
        comId: null,
        delFlag: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.iqcNos = selection.map((item) => item.iqcNo);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加iqc质检单据";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getBill(id).then((response) => {
        this.form = response.data;
        this.open = true;
        this.title = "修改iqc质检单据";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.id != null) {
            updateBill(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addBill(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      const iqcNos = row.iqcNo || this.iqcNos;
      console.log("iqcNos", row.iqcNo);
      this.$modal
        .confirm('是否确认删除iqc质检单据编号为"' + iqcNos + '"的数据项？')
        .then(function () {
          return delBill(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "quality/bill/export",
        {
          ...this.queryParams,
        },
        `iqc质检单据_${new Date().toLocaleDateString()}.xlsx`
      );
    },
  },
};
</script>
